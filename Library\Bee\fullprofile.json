{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 14000, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 14000, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 14000, "tid": 81, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 14000, "tid": 81, "ts": 1750357056921749, "dur": 74, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 14000, "tid": 81, "ts": 1750357056921870, "dur": 11, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 14000, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 14000, "tid": 1, "ts": 1750357056164951, "dur": 17484, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 14000, "tid": 1, "ts": 1750357056182506, "dur": 56072, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 14000, "tid": 1, "ts": 1750357056238582, "dur": 123593, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 14000, "tid": 81, "ts": 1750357056921885, "dur": 71, "ph": "X", "name": "", "args": {}}, {"pid": 14000, "tid": 68719476736, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056163616, "dur": 36401, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056200022, "dur": 718924, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056200046, "dur": 64, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056200114, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056200117, "dur": 570, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056200696, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056200701, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056200743, "dur": 54, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056200803, "dur": 4891, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056205707, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056205714, "dur": 124, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056205843, "dur": 6, "ph": "X", "name": "ProcessMessages 1869", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056205850, "dur": 43, "ph": "X", "name": "ReadAsync 1869", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056205897, "dur": 1, "ph": "X", "name": "ProcessMessages 29", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056205900, "dur": 42, "ph": "X", "name": "ReadAsync 29", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056205948, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056205952, "dur": 766, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056206725, "dur": 4, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056206731, "dur": 88, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056206822, "dur": 2, "ph": "X", "name": "ProcessMessages 882", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056206826, "dur": 126, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056206955, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056206958, "dur": 695, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056207658, "dur": 2, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056207662, "dur": 55, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056207720, "dur": 2, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056207723, "dur": 137, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056207864, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056207866, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056207937, "dur": 1, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056207939, "dur": 77, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208025, "dur": 4, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208031, "dur": 83, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208119, "dur": 2, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208123, "dur": 53, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208179, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208182, "dur": 57, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208241, "dur": 2, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208244, "dur": 62, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208309, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208312, "dur": 64, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208379, "dur": 1, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208381, "dur": 47, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208432, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208434, "dur": 39, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208478, "dur": 39, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208520, "dur": 1, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208522, "dur": 38, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208564, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208565, "dur": 135, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208705, "dur": 2, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208708, "dur": 50, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208761, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208764, "dur": 52, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208820, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208821, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208879, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208882, "dur": 70, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208955, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056208958, "dur": 67, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209028, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209031, "dur": 107, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209146, "dur": 3, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209151, "dur": 75, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209229, "dur": 2, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209233, "dur": 70, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209307, "dur": 1, "ph": "X", "name": "ProcessMessages 142", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209309, "dur": 61, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209373, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209376, "dur": 48, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209427, "dur": 1, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209430, "dur": 56, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209488, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209491, "dur": 53, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209547, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209550, "dur": 70, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209624, "dur": 1, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209628, "dur": 55, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209686, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209689, "dur": 47, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209739, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209742, "dur": 52, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209797, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209799, "dur": 56, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209858, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209861, "dur": 49, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209913, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209915, "dur": 49, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209967, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056209970, "dur": 61, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210035, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210037, "dur": 51, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210092, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210095, "dur": 58, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210156, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210159, "dur": 63, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210225, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210227, "dur": 55, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210285, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210288, "dur": 45, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210336, "dur": 1, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210339, "dur": 53, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210394, "dur": 1, "ph": "X", "name": "ProcessMessages 99", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210397, "dur": 61, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210461, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210463, "dur": 64, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210535, "dur": 3, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210540, "dur": 64, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210607, "dur": 1, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210609, "dur": 176, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210788, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210797, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210887, "dur": 1, "ph": "X", "name": "ProcessMessages 850", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210890, "dur": 69, "ph": "X", "name": "ReadAsync 850", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210966, "dur": 2, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056210970, "dur": 75, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211051, "dur": 2, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211055, "dur": 81, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211140, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211142, "dur": 64, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211211, "dur": 2, "ph": "X", "name": "ProcessMessages 911", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211215, "dur": 62, "ph": "X", "name": "ReadAsync 911", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211279, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211281, "dur": 43, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211326, "dur": 1, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211329, "dur": 46, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211378, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211380, "dur": 94, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211478, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211482, "dur": 51, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211536, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211538, "dur": 47, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211587, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211590, "dur": 42, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211634, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211637, "dur": 44, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211683, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211685, "dur": 48, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211736, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211739, "dur": 49, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211790, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211793, "dur": 59, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211855, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211858, "dur": 50, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211911, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211913, "dur": 28, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211946, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056211949, "dur": 55, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212007, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212016, "dur": 63, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212082, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212085, "dur": 45, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212132, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212135, "dur": 44, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212182, "dur": 1, "ph": "X", "name": "ProcessMessages 219", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212184, "dur": 48, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212237, "dur": 49, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212289, "dur": 2, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212292, "dur": 48, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212342, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212345, "dur": 45, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212393, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212396, "dur": 45, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212444, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212446, "dur": 51, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212503, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212505, "dur": 60, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212568, "dur": 3, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212573, "dur": 50, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212626, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212629, "dur": 47, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212678, "dur": 1, "ph": "X", "name": "ProcessMessages 174", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212681, "dur": 63, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212747, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212750, "dur": 75, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212829, "dur": 1, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212832, "dur": 85, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212921, "dur": 3, "ph": "X", "name": "ProcessMessages 1067", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212926, "dur": 54, "ph": "X", "name": "ReadAsync 1067", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212983, "dur": 1, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056212986, "dur": 49, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213039, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213041, "dur": 114, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213162, "dur": 4, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213168, "dur": 80, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213253, "dur": 3, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213257, "dur": 90, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213350, "dur": 1, "ph": "X", "name": "ProcessMessages 102", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213353, "dur": 41, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213396, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213398, "dur": 40, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213441, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213446, "dur": 53, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213501, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213504, "dur": 102, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213609, "dur": 2, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213613, "dur": 50, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213665, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213667, "dur": 66, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213736, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213739, "dur": 70, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213812, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213815, "dur": 53, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213870, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213873, "dur": 48, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213923, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213926, "dur": 68, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056213997, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214000, "dur": 46, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214049, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214051, "dur": 123, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214180, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214184, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214240, "dur": 2, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214243, "dur": 51, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214304, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214306, "dur": 67, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214380, "dur": 3, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214385, "dur": 84, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214474, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214477, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214553, "dur": 2, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214557, "dur": 159, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214723, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214727, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214827, "dur": 3, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214832, "dur": 76, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214913, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214916, "dur": 64, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214983, "dur": 2, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056214987, "dur": 35, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215025, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215029, "dur": 91, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215123, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215125, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215165, "dur": 1, "ph": "X", "name": "ProcessMessages 104", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215167, "dur": 33, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215203, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215205, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215248, "dur": 55, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215308, "dur": 1, "ph": "X", "name": "ProcessMessages 5", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215311, "dur": 45, "ph": "X", "name": "ReadAsync 5", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215359, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215362, "dur": 304, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215673, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215678, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215764, "dur": 3, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215777, "dur": 103, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215885, "dur": 1, "ph": "X", "name": "ProcessMessages 93", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215888, "dur": 61, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215953, "dur": 2, "ph": "X", "name": "ProcessMessages 162", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056215956, "dur": 113, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056216072, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056216074, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056216148, "dur": 2, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056216152, "dur": 80, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056216240, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056216243, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056216343, "dur": 2, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056216347, "dur": 82, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056216435, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056216438, "dur": 79, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056216522, "dur": 2, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056216526, "dur": 131, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056216662, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056216667, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056216776, "dur": 2, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056216780, "dur": 73, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056216860, "dur": 2, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056216865, "dur": 167, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217040, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217045, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217089, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217092, "dur": 56, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217152, "dur": 2, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217157, "dur": 74, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217234, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217237, "dur": 163, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217404, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217407, "dur": 53, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217471, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217475, "dur": 94, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217576, "dur": 2, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217582, "dur": 68, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217654, "dur": 3, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217660, "dur": 47, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217713, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217717, "dur": 56, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217779, "dur": 2, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217785, "dur": 90, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217880, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217884, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217939, "dur": 3, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217945, "dur": 43, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217992, "dur": 1, "ph": "X", "name": "ProcessMessages 152", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056217995, "dur": 87, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056218085, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056218095, "dur": 64, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056218164, "dur": 3, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056218168, "dur": 50, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056219019, "dur": 4, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056219028, "dur": 546, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056219581, "dur": 8, "ph": "X", "name": "ProcessMessages 4064", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056219591, "dur": 61, "ph": "X", "name": "ReadAsync 4064", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056219655, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056219658, "dur": 50, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056219711, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056219716, "dur": 62, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056219783, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056219789, "dur": 48, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056219841, "dur": 1, "ph": "X", "name": "ProcessMessages 106", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056219843, "dur": 75, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056219922, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056219926, "dur": 80, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220010, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220015, "dur": 59, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220077, "dur": 2, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220081, "dur": 49, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220133, "dur": 2, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220136, "dur": 54, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220195, "dur": 1, "ph": "X", "name": "ProcessMessages 131", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220198, "dur": 63, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220264, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220267, "dur": 89, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220361, "dur": 2, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220365, "dur": 79, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220448, "dur": 2, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220452, "dur": 62, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220519, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220522, "dur": 60, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220585, "dur": 2, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220589, "dur": 46, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220638, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220641, "dur": 171, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220816, "dur": 2, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220821, "dur": 83, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220908, "dur": 2, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056220911, "dur": 105, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056221023, "dur": 3, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056221028, "dur": 1092, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056223145, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056223150, "dur": 482, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056223637, "dur": 19, "ph": "X", "name": "ProcessMessages 13429", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056223659, "dur": 101, "ph": "X", "name": "ReadAsync 13429", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056223763, "dur": 2, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056223767, "dur": 45, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056223815, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056223817, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056223875, "dur": 2, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056223880, "dur": 58, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056223941, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056223944, "dur": 57, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224004, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224006, "dur": 48, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224057, "dur": 1, "ph": "X", "name": "ProcessMessages 198", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224060, "dur": 68, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224135, "dur": 3, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224140, "dur": 62, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224207, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224210, "dur": 232, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224446, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224449, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224521, "dur": 2, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224525, "dur": 50, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224578, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224581, "dur": 48, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224632, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224635, "dur": 50, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224688, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224691, "dur": 49, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224742, "dur": 1, "ph": "X", "name": "ProcessMessages 209", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224745, "dur": 50, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224798, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224802, "dur": 50, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224855, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224858, "dur": 59, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224919, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224922, "dur": 50, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224975, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056224978, "dur": 62, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225042, "dur": 2, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225048, "dur": 53, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225104, "dur": 1, "ph": "X", "name": "ProcessMessages 217", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225106, "dur": 45, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225155, "dur": 1, "ph": "X", "name": "ProcessMessages 211", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225157, "dur": 46, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225206, "dur": 2, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225209, "dur": 80, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225293, "dur": 2, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225296, "dur": 49, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225349, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225351, "dur": 60, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225414, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225417, "dur": 45, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225465, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225468, "dur": 56, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225527, "dur": 1, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225530, "dur": 47, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225580, "dur": 7, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225589, "dur": 48, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225640, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225643, "dur": 91, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225738, "dur": 2, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225742, "dur": 65, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225811, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225814, "dur": 52, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225870, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225873, "dur": 75, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225951, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056225954, "dur": 51, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226010, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226012, "dur": 76, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226091, "dur": 2, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226094, "dur": 111, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226214, "dur": 2, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226218, "dur": 61, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226284, "dur": 2, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226288, "dur": 39, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226332, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226336, "dur": 41, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226381, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226383, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226443, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226446, "dur": 49, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226498, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226505, "dur": 49, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226557, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226560, "dur": 57, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226620, "dur": 2, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226623, "dur": 90, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226720, "dur": 3, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226739, "dur": 101, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226845, "dur": 5, "ph": "X", "name": "ProcessMessages 891", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226855, "dur": 77, "ph": "X", "name": "ReadAsync 891", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226937, "dur": 2, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056226942, "dur": 77, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227024, "dur": 3, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227028, "dur": 73, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227104, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227109, "dur": 146, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227258, "dur": 1, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227261, "dur": 51, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227317, "dur": 2, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227320, "dur": 63, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227387, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227389, "dur": 87, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227482, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227483, "dur": 87, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227573, "dur": 2, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227577, "dur": 49, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227629, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227631, "dur": 55, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227689, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227692, "dur": 51, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227746, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227749, "dur": 57, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227809, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227813, "dur": 46, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227862, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056227864, "dur": 204, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228072, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228075, "dur": 51, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228130, "dur": 3, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228135, "dur": 55, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228194, "dur": 1, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228196, "dur": 59, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228258, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228260, "dur": 53, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228317, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228320, "dur": 50, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228373, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228376, "dur": 54, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228433, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228435, "dur": 49, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228488, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228490, "dur": 48, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228541, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228544, "dur": 44, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228591, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228594, "dur": 93, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228690, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228692, "dur": 51, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228746, "dur": 2, "ph": "X", "name": "ProcessMessages 735", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228750, "dur": 41, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228793, "dur": 1, "ph": "X", "name": "ProcessMessages 10", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228796, "dur": 60, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228859, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228862, "dur": 53, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228918, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228921, "dur": 44, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228968, "dur": 1, "ph": "X", "name": "ProcessMessages 153", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056228971, "dur": 82, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056229056, "dur": 1, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056229058, "dur": 61, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056229126, "dur": 3, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056229136, "dur": 99, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056229241, "dur": 2, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056229244, "dur": 76, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056229326, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056229329, "dur": 1025, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230361, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230368, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230442, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230447, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230487, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230491, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230533, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230537, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230588, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230590, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230632, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230634, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230676, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230679, "dur": 127, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230809, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230811, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230853, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230855, "dur": 57, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230915, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230917, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230953, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056230956, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056231303, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056231316, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056231390, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056231394, "dur": 254, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056231655, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056231659, "dur": 203, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056231866, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056231870, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056231934, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056231937, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232002, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232006, "dur": 156, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232168, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232172, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232215, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232218, "dur": 159, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232383, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232386, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232489, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232493, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232565, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232568, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232621, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232624, "dur": 86, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232713, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232715, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232754, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232756, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232863, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056232867, "dur": 185, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233057, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233061, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233125, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233129, "dur": 67, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233202, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233206, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233267, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233270, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233338, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233341, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233485, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233488, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233554, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233557, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233641, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233646, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233717, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233721, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233772, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233775, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233831, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233833, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233885, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233903, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056233996, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234000, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234068, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234072, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234152, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234155, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234216, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234220, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234291, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234301, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234359, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234362, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234410, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234414, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234499, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234502, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234551, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234555, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234607, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234610, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234645, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234647, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234686, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234719, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234722, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234762, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234764, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234824, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234826, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234860, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234863, "dur": 73, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234939, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234941, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234976, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056234978, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235014, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235016, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235055, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235058, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235099, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235134, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235137, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235173, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235175, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235226, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235228, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235261, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235265, "dur": 64, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235333, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235335, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235371, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235373, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235409, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235412, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235448, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235450, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235507, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235509, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235575, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235578, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235612, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235615, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235666, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235670, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235734, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235737, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235772, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235775, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235811, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235814, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235889, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235892, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235937, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235941, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235985, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056235989, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236080, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236082, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236119, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236122, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236157, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236159, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236263, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236266, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236302, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236305, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236346, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236351, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236387, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236389, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236423, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236457, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236460, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236493, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236495, "dur": 92, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236590, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236592, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236627, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236629, "dur": 43, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236675, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236677, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236709, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236711, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236758, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236799, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236812, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236854, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236856, "dur": 31, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236890, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236892, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236928, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236930, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056236992, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237036, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237039, "dur": 30, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237072, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237074, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237143, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237145, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237177, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237180, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237212, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237214, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237274, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237276, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237311, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237314, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237346, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237348, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237384, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237386, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237463, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237465, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237501, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237503, "dur": 43, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237549, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237551, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237586, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237588, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237621, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237623, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237683, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237685, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237732, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237735, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237774, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237776, "dur": 110, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237891, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237894, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237954, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056237957, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238017, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238019, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238068, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238071, "dur": 96, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238171, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238174, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238211, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238213, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238246, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238248, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238281, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238283, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238315, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238317, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238354, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238394, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238396, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238435, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238490, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238493, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238558, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238568, "dur": 63, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238635, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238637, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238691, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238694, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238735, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238738, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238776, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238779, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238822, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238827, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238884, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238886, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238924, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238926, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238964, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056238966, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239013, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239015, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239052, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239057, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239100, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239102, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239138, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239140, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239175, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239177, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239210, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239212, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239253, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239255, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239290, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239293, "dur": 64, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239359, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239362, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239401, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239404, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239441, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239443, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239479, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239481, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239553, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239556, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239608, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239610, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239654, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239657, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239729, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239770, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239773, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239812, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239815, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239871, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239873, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239912, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239915, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239956, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239958, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239990, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056239992, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240046, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240049, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240094, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240096, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240168, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240171, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240210, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240213, "dur": 77, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240293, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240295, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240332, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240334, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240375, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240377, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240422, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240424, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240460, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240462, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240498, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240501, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240548, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240551, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240589, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240591, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240635, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240637, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240694, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240697, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240736, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240739, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240773, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240775, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240808, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240810, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240846, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240849, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240909, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240913, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240957, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240961, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056240997, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056241002, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056241041, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056241043, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056241092, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056241094, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056241130, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056241132, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056241194, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056241197, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056241235, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056241237, "dur": 428, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056241670, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056241673, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056241714, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056241716, "dur": 2216, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056243937, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056243941, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056243985, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056243987, "dur": 13288, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056257287, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056257295, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056257363, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056257367, "dur": 63, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056257434, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056257437, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056257477, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056257480, "dur": 70, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056257557, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056257560, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056257620, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056257624, "dur": 336, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056257964, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056257967, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056258000, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056258003, "dur": 1597, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056259609, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056259613, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056259667, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056259669, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056259718, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056259720, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056259784, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056259786, "dur": 197, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056259993, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056259997, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056260079, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056260081, "dur": 838, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056260925, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056260928, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056260972, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056260974, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261021, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261023, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261061, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261064, "dur": 294, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261363, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261366, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261413, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261415, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261500, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261503, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261542, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261547, "dur": 116, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261666, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261668, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261718, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261720, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261757, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261760, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261817, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056261819, "dur": 371, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262195, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262198, "dur": 181, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262382, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262385, "dur": 155, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262545, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262548, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262589, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262592, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262657, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262694, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262696, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262734, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262736, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262792, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262794, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262831, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262833, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262901, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262903, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262939, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056262942, "dur": 64, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263009, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263011, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263059, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263061, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263106, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263109, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263144, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263146, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263171, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263222, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263224, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263260, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263262, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263297, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263299, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263333, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263335, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263375, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263377, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263411, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263413, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263493, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263496, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263548, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263552, "dur": 76, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263631, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263634, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263672, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263674, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263733, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263783, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263786, "dur": 180, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263969, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056263971, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264006, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264009, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264072, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264074, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264108, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264110, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264202, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264204, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264258, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264261, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264301, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264303, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264341, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264343, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264385, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264418, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264420, "dur": 175, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264602, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264606, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264711, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264714, "dur": 116, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264834, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264837, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264876, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264878, "dur": 63, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264947, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056264951, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056265017, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056265020, "dur": 218, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056265242, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056265246, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056265300, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056265303, "dur": 235, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056265544, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056265547, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056265639, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056265643, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056265706, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056265708, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056265746, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056265749, "dur": 106, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056265860, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056265864, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056265921, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056265926, "dur": 148, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056266078, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056266081, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056266122, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056266125, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056266170, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056266172, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056266287, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056266291, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056266405, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056266408, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056266473, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056266485, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056266530, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056266532, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056266604, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056266607, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056266661, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056266664, "dur": 335, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267004, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267007, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267068, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267071, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267151, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267154, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267226, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267229, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267352, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267355, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267410, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267413, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267509, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267512, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267580, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267583, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267679, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267682, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267724, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267728, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267777, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267780, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267829, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056267831, "dur": 182, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268017, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268019, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268080, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268083, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268164, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268166, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268206, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268209, "dur": 99, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268314, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268318, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268367, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268370, "dur": 162, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268536, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268538, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268593, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268596, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268690, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268693, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268766, "dur": 5, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268775, "dur": 39, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268816, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268819, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268867, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056268870, "dur": 271, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056269148, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056269152, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056269195, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056269198, "dur": 478, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056269682, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056269686, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056269731, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056269737, "dur": 703, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056270444, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056270448, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056270513, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056270517, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056270947, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056270951, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056271002, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056271005, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056271074, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056271076, "dur": 607770, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056878872, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056878880, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056878948, "dur": 156, "ph": "X", "name": "ProcessMessages 3060", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056879112, "dur": 5425, "ph": "X", "name": "ReadAsync 3060", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056884552, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056884559, "dur": 124, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056884692, "dur": 43, "ph": "X", "name": "ProcessMessages 7502", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056884739, "dur": 22502, "ph": "X", "name": "ReadAsync 7502", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056907254, "dur": 480, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056907742, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056907838, "dur": 4, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 14000, "tid": 68719476736, "ts": 1750357056907844, "dur": 11075, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 14000, "tid": 81, "ts": 1750357056921960, "dur": 7269, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 14000, "tid": 64424509440, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 14000, "tid": 64424509440, "ts": 1750357056152671, "dur": 209548, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 14000, "tid": 64424509440, "ts": 1750357056362221, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 14000, "tid": 64424509440, "ts": 1750357056362224, "dur": 128, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 14000, "tid": 81, "ts": 1750357056929239, "dur": 17, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 14000, "tid": 60129542144, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 14000, "tid": 60129542144, "ts": 1750357056132260, "dur": 786784, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 14000, "tid": 60129542144, "ts": 1750357056134964, "dur": 16708, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 14000, "tid": 60129542144, "ts": 1750357056919058, "dur": 319, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 14000, "tid": 60129542144, "ts": 1750357056919104, "dur": 50, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 14000, "tid": 60129542144, "ts": 1750357056919381, "dur": 3, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 14000, "tid": 81, "ts": 1750357056929331, "dur": 15, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750357056199690, "dur": 226, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750357056200016, "dur": 3699, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750357056203733, "dur": 1429, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750357056205288, "dur": 71, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1750357056205360, "dur": 852, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750357056206543, "dur": 146, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BD066AF91216C895.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056206765, "dur": 678, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_916E5E16AB469CF0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056207453, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_F5DF255C08D11A8C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056207569, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C7FDD92EE7195664.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056207651, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_747257F2B95C7621.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056207718, "dur": 681, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_F2C1277DFF099C79.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056208633, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_9A7786E72A6B5E8C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056208871, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_2FD890D4BBB4CE26.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056209209, "dur": 138, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_1E51E4CC49144E2E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056209357, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_A8E13D05E0B11793.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056209637, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_6321B8555A1A761D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056209800, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_C8FB050F2C14EF55.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056209891, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_2DC85C64375951B1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056209978, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_E84E2AFCCEFD9CC7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056210109, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_9DA4C3C21022D7A6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056210322, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_60DA01DF63CFD649.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056210455, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_42F7D41B93B94AC7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056210610, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_33A63B48735421F9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056210808, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E0273E4BBA1C33C0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056210931, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_C412A4EF09BA29D5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056211038, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_D65E8F5246B9B178.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056211138, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D0F1A61EB4189FC9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056211207, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BD985A39D270C9B7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056211271, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_C33E9E8B76E14557.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056211601, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_D23AD71C5DDFE1B4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056211702, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_6C05DAAF3985B5DA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056211797, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_49461019CED446C6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056211964, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_606993901B0ABE27.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056212041, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_37A91187EFAA1E4D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056212107, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_167AF4DBC06E2AE6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056212188, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_1BC2F9751197887F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056212304, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_81157A417001B5EA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056212444, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_B1A9D295A62A6B25.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056212842, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9B5BF5B045BFFED8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056212958, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_CE52468F2AF94D3E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056213218, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_AE7AE761007ACD6C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056213327, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_361A65A56C957CE2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056213492, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_988D394CD4FBAB22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056213677, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_1A8907DE4FA741CC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056213765, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_E0FCC25CAA3CC2C8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056213901, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_686B6CE4ABC74178.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056213995, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_58AFBFB335E63D9E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056214055, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_368E6BDD3809CE5E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056214263, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_E1DE8996EB0BAECB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056214381, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_A3EA7E1937C99F5B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056214568, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750357056214687, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056214750, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750357056215015, "dur": 104, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056215478, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750357056215561, "dur": 163, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750357056215734, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056215911, "dur": 222, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750357056216432, "dur": 254, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056216717, "dur": 348, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750357056217074, "dur": 158, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750357056217269, "dur": 190, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750357056217499, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056217931, "dur": 276, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750357056218308, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750357056218397, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056218460, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750357056218681, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750357056218801, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750357056218936, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056219157, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750357056219360, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056219651, "dur": 427, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056220096, "dur": 239, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750357056220425, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Splines.ref.dll_99352414147018AA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056220529, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750357056220668, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750357056220830, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750357056221029, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750357056221184, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750357056221261, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056221336, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750357056221641, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056221727, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750357056222864, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Splines.Editor.ref.dll_FBEF939465B2E7A7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056223984, "dur": 448, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750357056224448, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750357056224521, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750357056224632, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750357056224768, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Interfaces.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750357056224830, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750357056224887, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750357056225285, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056225442, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Interfaces.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750357056225678, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750357056225804, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750357056225875, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Battle.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750357056225934, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750357056226162, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750357056226280, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Battle.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750357056226502, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750357056226629, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056226865, "dur": 128, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750357056227118, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750357056227199, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750357056227256, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Characters.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750357056227391, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750357056227655, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750357056227768, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750357056227927, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750357056228093, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Cinemachine.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750357056228149, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Splines.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750357056228316, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750357056228444, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750357056228681, "dur": 125, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.Camera.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750357056228887, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750357056229002, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750357056229074, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750357056229244, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056229401, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750357056229519, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750357056229679, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750357056229785, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750357056206290, "dur": 23698, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750357056229988, "dur": 80, "ph": "X", "name": "SortWorkingStack", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750357056230070, "dur": 666170, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750357056896242, "dur": 276, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750357056896553, "dur": 51, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750357056907877, "dur": 77, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750357056907995, "dur": 2870, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750357056206475, "dur": 23658, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056230166, "dur": 1283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_AE7AE761007ACD6C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357056231464, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056231603, "dur": 3811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_BF8B01E2682946F2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357056235415, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056235499, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_42F7D41B93B94AC7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357056235568, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056235636, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_9283034F04690CF9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357056235699, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056235767, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_DCCA835A7B0055CC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357056235842, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056235900, "dur": 777, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_DCCA835A7B0055CC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357056236679, "dur": 2255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_F2C1277DFF099C79.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357056239123, "dur": 284, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1750357056239457, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056239522, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056239584, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056239646, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056239708, "dur": 660, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056240398, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056240551, "dur": 999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750357056241551, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056241789, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1750357056241856, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056242833, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056243601, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056245328, "dur": 609, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Editor\\Editors\\CinemachineFollowEditor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1750357056244323, "dur": 1858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056246181, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056247339, "dur": 1067, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Runtime\\Behaviours\\CinemachineSequencerCamera.cs"}}, {"pid": 12345, "tid": 1, "ts": 1750357056248586, "dur": 764, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Runtime\\Behaviours\\CinemachineMixingCamera.cs"}}, {"pid": 12345, "tid": 1, "ts": 1750357056246863, "dur": 2566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056249429, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056250594, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056251416, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056252152, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056252918, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056253812, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056254503, "dur": 1692, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b2\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 1, "ts": 1750357056254503, "dur": 2810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056257314, "dur": 837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056258151, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056258251, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056258353, "dur": 2038, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056260391, "dur": 1421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056261852, "dur": 1206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357056263060, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056263142, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357056263336, "dur": 1182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750357056264519, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056264779, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056264873, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750357056265562, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056265658, "dur": 976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750357056266636, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056266870, "dur": 777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750357056267656, "dur": 936, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750357056270687, "dur": 614632, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750357056206616, "dur": 23567, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056230201, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_5C4DA00CC016DBF5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056230982, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056231147, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_16F59DAED9B12573.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056231229, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056231301, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_D437D5B52E940013.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056231372, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056233187, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_D910E96BA6DA4E34.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056233376, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056233593, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_D910E96BA6DA4E34.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056233687, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_908A7CF0F95B5EED.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056233784, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056233855, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_C33E9E8B76E14557.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056233922, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056233985, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D0F1A61EB4189FC9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056234152, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056234293, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F5F9D4CEA3FE42FE.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056234422, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056234510, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_F0A87365319D71DD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056234998, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056235132, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1FC85B3DDABA9491.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056235197, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056235283, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_33A63B48735421F9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056235577, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056235737, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_A02BE6282932C1CB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056235951, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056236025, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_97DB01ADA77F4791.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056236109, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056236176, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DDA69634F904366D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056236238, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056236310, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F591B0E4ADD29A56.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056236377, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056236441, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_1A8907DE4FA741CC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056236509, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056236760, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_747257F2B95C7621.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056236855, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056236923, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_BAA7B95D94018A98.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056236992, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056237062, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_916E5E16AB469CF0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056237125, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056237182, "dur": 1636, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_916E5E16AB469CF0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056238822, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056238958, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750357056239009, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056239076, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056239130, "dur": 277, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1750357056239424, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056239580, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750357056239635, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056239737, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056239822, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056239925, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056239988, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056240056, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056240118, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1750357056240543, "dur": 1846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750357056242390, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056242462, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056243732, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056245843, "dur": 1814, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines@b909627b5095\\Editor\\GUI\\Editors\\KnotReorderableList.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750357056244902, "dur": 2932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056247834, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056248993, "dur": 3379, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\affine_transform.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750357056248842, "dur": 4179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056253022, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056253840, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056254663, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056255523, "dur": 646, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@b10ae3e6f84c\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IAssemblyLoadProxy.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750357056255267, "dur": 1321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056256588, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056257371, "dur": 778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056258149, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056258289, "dur": 2149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056260438, "dur": 1307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056261751, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056261855, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056262095, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056262419, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750357056262524, "dur": 910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750357056263435, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056263726, "dur": 988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750357056264715, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056265045, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056265185, "dur": 1085, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056266283, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056266371, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056267051, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056267238, "dur": 529, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056267792, "dur": 742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056268536, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056268947, "dur": 663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056269610, "dur": 1883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056271493, "dur": 97539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750357056369033, "dur": 527191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056206569, "dur": 23645, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056230231, "dur": 908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_2E76C9AC399D6AFC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357056231141, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056231222, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9B5BF5B045BFFED8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357056231328, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056231390, "dur": 4143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9B5BF5B045BFFED8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357056235537, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_60DA01DF63CFD649.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357056235727, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056235987, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_47DDA486F86DF89C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357056236130, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056236299, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_47DDA486F86DF89C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357056236367, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_7417C209D47BDBE3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357056236500, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056236943, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_416ADE92277E457E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357056237239, "dur": 1548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_609C16FA6C001C3C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357056238787, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056238908, "dur": 281, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_609C16FA6C001C3C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357056239198, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357056239867, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056239931, "dur": 17911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750357056257845, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056258081, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056258362, "dur": 2020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056260389, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056260506, "dur": 1249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056261756, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056262226, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357056262514, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056262592, "dur": 746, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Characters.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357056263340, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056263900, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750357056264455, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056264533, "dur": 1460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750357056265994, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056266792, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056266932, "dur": 1428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750357056268367, "dur": 136, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750357056270237, "dur": 609667, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750357056892705, "dur": 3533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056206697, "dur": 23742, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056230454, "dur": 677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_28E7CDA6A9657C3E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056231132, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056231464, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_F78CC16BE3410205.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056231538, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056231617, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_DB43258EFB737D47.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056231689, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056231760, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_81157A417001B5EA.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056231825, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056231888, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_167AF4DBC06E2AE6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056231963, "dur": 3648, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056235677, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056235866, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056236428, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056236531, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_D9B7D3234F369077.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056236801, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056236913, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C7FDD92EE7195664.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056237041, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056237198, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C7FDD92EE7195664.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056237286, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ClothModule.dll_D461DBDF1BF3D038.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056237387, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056237496, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_567619744B52B229.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056237579, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056237650, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A9C666157FD2E5AB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056237740, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056237863, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_988D394CD4FBAB22.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056237932, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056237998, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_361A65A56C957CE2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056238060, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056238122, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_A3EA7E1937C99F5B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056238187, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056238264, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_85E4A9D67E927DC7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056238329, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056238410, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056238481, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056238543, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_2A580A41823500EB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056238604, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056238659, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_2A580A41823500EB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056238904, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056239098, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750357056239211, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056239277, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056239471, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056239535, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056239689, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056239770, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056239841, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750357056239900, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056240008, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056240075, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056240136, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056240200, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056240258, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750357056240575, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056240688, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750357056240758, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056241091, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1750357056241275, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056241625, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056241744, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056241808, "dur": 2911, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1750357056244721, "dur": 1549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056246271, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056247349, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056248481, "dur": 1250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056249732, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056250847, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056251904, "dur": 1237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056253142, "dur": 1128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056254997, "dur": 607, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b2\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750357056254270, "dur": 1630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056256058, "dur": 770, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@b10ae3e6f84c\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableMaxTimeCommand.cs"}}, {"pid": 12345, "tid": 4, "ts": 1750357056255900, "dur": 1807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056257707, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056258032, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056258140, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056258327, "dur": 2084, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056260411, "dur": 1376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056261787, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056262183, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056262417, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056262479, "dur": 546, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056263027, "dur": 908, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056263939, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056264207, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056264274, "dur": 766, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056265044, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056265122, "dur": 1150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056266384, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056266521, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056266848, "dur": 913, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056267778, "dur": 774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056268553, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056268933, "dur": 685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056269618, "dur": 1851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056271472, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750357056271711, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750357056271840, "dur": 624335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056206817, "dur": 23656, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056230485, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3A585D315D7CDFE1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357056231060, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056231251, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2A38684436712F05.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357056231380, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056231465, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_D1622255EC84C8DF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357056231543, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056231612, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_564C15F1D50DDD04.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357056231686, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056231754, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_0399CE90B9A515B4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357056231818, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056231884, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_1BC2F9751197887F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357056231961, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056232067, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_37A91187EFAA1E4D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357056232329, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056232397, "dur": 3568, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_37A91187EFAA1E4D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357056236013, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_6321B8555A1A761D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357056236123, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056236352, "dur": 237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_6321B8555A1A761D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357056236602, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_9A7786E72A6B5E8C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357056237041, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056237179, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_9A7786E72A6B5E8C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357056237264, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_BD066AF91216C895.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357056237600, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056237820, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_93BF4D5398025317.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357056237936, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056238118, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056238267, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750357056238353, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056238502, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_997BF679851B602C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357056238686, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750357056238802, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056238860, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750357056238973, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056239038, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750357056239097, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056239178, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056239240, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056239323, "dur": 487, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056239816, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750357056239886, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056240234, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056240342, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1750357056240443, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750357056240502, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056240595, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750357056241086, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056241178, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056241255, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056241355, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056241461, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056241536, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056241598, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056241673, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056241746, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056241887, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056243022, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056244663, "dur": 684, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine@b66fdb7cd1f2\\Editor\\Obsolete\\CinemachineTransposerEditor.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750357056244218, "dur": 1854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056246073, "dur": 1115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056247188, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056248291, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056249622, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056250703, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056251783, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056253035, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056254140, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056255313, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056256494, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056257561, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056258041, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056258133, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056258346, "dur": 2056, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056260403, "dur": 1408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056261811, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056262181, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357056262413, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056262522, "dur": 1050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750357056263573, "dur": 375, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056263964, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056264038, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056264102, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056264333, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056264865, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056264999, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357056265261, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056265349, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750357056265474, "dur": 2148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750357056267624, "dur": 464, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056268116, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056268308, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056268477, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056268572, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056268876, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056268960, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056269443, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056269601, "dur": 1905, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056271506, "dur": 621206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750357056892714, "dur": 3537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056206875, "dur": 23634, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056230530, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_EF62AA7CD312F942.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056231001, "dur": 704, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056231713, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_C62338721BDB794D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056231910, "dur": 554, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056232465, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_C62338721BDB794D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056232649, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_5E1525907236878A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056232726, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056232951, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_B1A9D295A62A6B25.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056233025, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056233165, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_49461019CED446C6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056233243, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056233351, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_6C05DAAF3985B5DA.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056233476, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056233780, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_C44092216EEC6649.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056233851, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056234076, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F9164AECFF7EBE63.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056234185, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056234415, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F9164AECFF7EBE63.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056234624, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_C368AB8F525C9F58.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056234807, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056235012, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056235209, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056235403, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_1A4CC6E2C0ED3512.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056235541, "dur": 1917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_4C2401B10833EC61.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056237459, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056237650, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056237791, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_BEDC949A65E52DA7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056237973, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056238074, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_B701B6B92B8026DD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056238236, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056238345, "dur": 639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750357056238984, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056239322, "dur": 1129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056240452, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056240518, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056240588, "dur": 17406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750357056257995, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056258250, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056258327, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_E55D0F7C63F01D9E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056258394, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056258659, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056258751, "dur": 1392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750357056260145, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056260389, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056260469, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056260675, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056260746, "dur": 777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750357056261524, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056261747, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056261838, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056262078, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056262151, "dur": 1389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750357056263542, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056263739, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056263812, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056264183, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056264466, "dur": 901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750357056265368, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056265664, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056265733, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056265953, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056266023, "dur": 2597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750357056268621, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056268816, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056269100, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750357056269314, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056269530, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056269614, "dur": 1865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056271479, "dur": 91721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056367629, "dur": 1386, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1750357056363202, "dur": 5823, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750357056369026, "dur": 527146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056206969, "dur": 23614, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056230591, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_163A7D00BA7D065C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056230983, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056231110, "dur": 1625, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_163A7D00BA7D065C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056232738, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_AC3B8AED076F4CEB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056232845, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056232957, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_9A1C02F8E98C469D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056233150, "dur": 346, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056233507, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_D23AD71C5DDFE1B4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056233642, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056233952, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BD985A39D270C9B7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056234060, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056234257, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_D65E8F5246B9B178.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056234323, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056234444, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_C412A4EF09BA29D5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056234676, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056234779, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E0273E4BBA1C33C0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056234941, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056235077, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_6648D96BDBBB29C0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056235235, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056235382, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_B800F4FB36E290D5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056235466, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056235533, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_AC1145378078C087.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056235595, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056235655, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_9DA4C3C21022D7A6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056235722, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056235788, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_2DC85C64375951B1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056235857, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056235920, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_5592C7A2A0C2A571.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056235979, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056236044, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_A8E13D05E0B11793.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056236106, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056236170, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_CB9C52C6DA4AB85A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056236235, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056236302, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_B3223AFAAE7F432F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056236364, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056236426, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_8E9AC1970AF2CEA5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056236496, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056236561, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_31E341F8DD700DC8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056237207, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056237271, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_1F322CB3825CC582.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056237352, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056237421, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_BFE30F898F3B693C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056237487, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056237560, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_AF1FC69261D1616D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056237625, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056237690, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_686B6CE4ABC74178.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056237754, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056237822, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4B6EE49D6AF37933.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056237885, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056237952, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_8D8B6A3723BAC276.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056238014, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056238098, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056238163, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UWP.Extensions.dll_7000EBAFC0872D26.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056238225, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056238294, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056238361, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750357056238433, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056238496, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056238559, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750357056239164, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056239285, "dur": 1053, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056240362, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1750357056240769, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056240953, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1750357056241068, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056241383, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056241588, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056241988, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056242827, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056243598, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056244328, "dur": 1707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056246036, "dur": 1086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056247122, "dur": 1112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056249445, "dur": 3629, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\int2x3.gen.cs"}}, {"pid": 12345, "tid": 7, "ts": 1750357056248235, "dur": 4982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056253217, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056254411, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056255584, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056256751, "dur": 651, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.2.0b2\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750357056256696, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056257416, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056258118, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056258354, "dur": 2035, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056260389, "dur": 1486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056261876, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056262216, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056262315, "dur": 854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750357056263171, "dur": 519, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056263778, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056263907, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750357056264335, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056264456, "dur": 2020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750357056266477, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056266668, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056267086, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056267167, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056267287, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056267383, "dur": 1174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056268558, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056268830, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056268968, "dur": 662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056269630, "dur": 1675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056271311, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056271386, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750357056271531, "dur": 624692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056207002, "dur": 23604, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056230607, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_CE52468F2AF94D3E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357056230983, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056231106, "dur": 774, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_CE52468F2AF94D3E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357056231882, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_8F9DC40C34B6425C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357056232320, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056232375, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_8F9DC40C34B6425C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357056232456, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_606993901B0ABE27.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357056232604, "dur": 3803, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_606993901B0ABE27.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357056236410, "dur": 914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_2FD890D4BBB4CE26.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357056237325, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056237406, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_9F4733395C159688.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357056237610, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056237832, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056237944, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_7614754D03CD3CB4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357056238480, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056238699, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056238759, "dur": 1195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1750357056239956, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750357056240196, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1750357056240570, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056240683, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056240759, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056240822, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056240926, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056240989, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750357056241040, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056241100, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750357056241156, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056241223, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056241300, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056241360, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056241423, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056241490, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056241590, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056241699, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056241820, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056241891, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056243108, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056244302, "dur": 1509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056245812, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056246617, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056247630, "dur": 2233, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines@b909627b5095\\Runtime\\ISplineProvider.cs"}}, {"pid": 12345, "tid": 8, "ts": 1750357056247265, "dur": 3069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056250334, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056251079, "dur": 1439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056252519, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056253304, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056254027, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056254768, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056255505, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056256258, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056257142, "dur": 1015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056258158, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056258248, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056258395, "dur": 1990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056260391, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056260516, "dur": 1224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056261745, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056261840, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357056262075, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056262322, "dur": 858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750357056263181, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056263370, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056263460, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056263527, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056263589, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056263941, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056264026, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056264091, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056264155, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056264279, "dur": 758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056265039, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357056265342, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056265411, "dur": 215, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357056265628, "dur": 639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056266269, "dur": 1389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357056267659, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056267935, "dur": 1131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750357056269068, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056269396, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056269646, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357056269855, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056269947, "dur": 1052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750357056271001, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056271248, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056271396, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750357056271678, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750357056271757, "dur": 624452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750357056914876, "dur": 3685, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 14000, "tid": 81, "ts": 1750357056929546, "dur": 20088, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 14000, "tid": 81, "ts": 1750357056950047, "dur": 11766, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 14000, "tid": 81, "ts": 1750357056921832, "dur": 40074, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}