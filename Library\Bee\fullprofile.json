{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 14000, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 14000, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 14000, "tid": 20, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 14000, "tid": 20, "ts": 1750356698497701, "dur": 34, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 14000, "tid": 20, "ts": 1750356698497749, "dur": 6, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 14000, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 14000, "tid": 1, "ts": 1750356698204158, "dur": 3665, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 14000, "tid": 1, "ts": 1750356698207828, "dur": 22879, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 14000, "tid": 1, "ts": 1750356698230711, "dur": 26306, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 14000, "tid": 20, "ts": 1750356698497759, "dur": 39, "ph": "X", "name": "", "args": {}}, {"pid": 14000, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698204094, "dur": 25170, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698229266, "dur": 266803, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698229276, "dur": 219, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698229499, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698229501, "dur": 1023, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698230534, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698230538, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698230596, "dur": 8, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698230606, "dur": 4149, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698234776, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698234781, "dur": 300, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698235084, "dur": 16, "ph": "X", "name": "ProcessMessages 17754", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698235101, "dur": 122, "ph": "X", "name": "ReadAsync 17754", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698235228, "dur": 2, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698235231, "dur": 490, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698235726, "dur": 2, "ph": "X", "name": "ProcessMessages 1651", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698235730, "dur": 35, "ph": "X", "name": "ReadAsync 1651", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698235769, "dur": 51, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698235824, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698235825, "dur": 163, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698235991, "dur": 3, "ph": "X", "name": "ProcessMessages 3052", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698235995, "dur": 70, "ph": "X", "name": "ReadAsync 3052", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236068, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236070, "dur": 71, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236144, "dur": 1, "ph": "X", "name": "ProcessMessages 1122", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236146, "dur": 57, "ph": "X", "name": "ReadAsync 1122", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236206, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236208, "dur": 52, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236263, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236265, "dur": 72, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236340, "dur": 1, "ph": "X", "name": "ProcessMessages 1158", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236342, "dur": 58, "ph": "X", "name": "ReadAsync 1158", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236404, "dur": 1, "ph": "X", "name": "ProcessMessages 1064", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236406, "dur": 54, "ph": "X", "name": "ReadAsync 1064", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236462, "dur": 1, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236464, "dur": 47, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236515, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236516, "dur": 177, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236698, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236701, "dur": 198, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236901, "dur": 3, "ph": "X", "name": "ProcessMessages 2450", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698236906, "dur": 240, "ph": "X", "name": "ReadAsync 2450", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698237150, "dur": 3, "ph": "X", "name": "ProcessMessages 2454", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698237154, "dur": 60, "ph": "X", "name": "ReadAsync 2454", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698237216, "dur": 1, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698237219, "dur": 42, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698237266, "dur": 2, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698237270, "dur": 184, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698237458, "dur": 2, "ph": "X", "name": "ProcessMessages 926", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698237461, "dur": 62, "ph": "X", "name": "ReadAsync 926", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698237526, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698237529, "dur": 484, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698238016, "dur": 1, "ph": "X", "name": "ProcessMessages 151", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698238018, "dur": 56, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698238077, "dur": 1, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698238080, "dur": 43, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698238127, "dur": 156, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698238286, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698238288, "dur": 169, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698238460, "dur": 2, "ph": "X", "name": "ProcessMessages 1551", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698238463, "dur": 45, "ph": "X", "name": "ReadAsync 1551", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698238511, "dur": 1, "ph": "X", "name": "ProcessMessages 166", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698238513, "dur": 53, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698238571, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698238573, "dur": 1845, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698240424, "dur": 20, "ph": "X", "name": "ProcessMessages 20536", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698240445, "dur": 551, "ph": "X", "name": "ReadAsync 20536", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698241000, "dur": 3, "ph": "X", "name": "ProcessMessages 2150", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698241004, "dur": 158, "ph": "X", "name": "ReadAsync 2150", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698241165, "dur": 2, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698241169, "dur": 59, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698241231, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698241233, "dur": 56, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698241293, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698241295, "dur": 1057, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698245607, "dur": 6, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698245616, "dur": 893, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698246515, "dur": 20, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698246536, "dur": 1007, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698247975, "dur": 130, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698248112, "dur": 289, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698249882, "dur": 7, "ph": "X", "name": "ProcessMessages 2193", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698249891, "dur": 140, "ph": "X", "name": "ReadAsync 2193", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698250034, "dur": 5, "ph": "X", "name": "ProcessMessages 2452", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698250040, "dur": 497, "ph": "X", "name": "ReadAsync 2452", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698250541, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698250545, "dur": 77, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698250625, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698250627, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698250684, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698250711, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698250713, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698250751, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698250795, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698250887, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698250890, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698250937, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698250939, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698250971, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698251001, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698251039, "dur": 854, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698251906, "dur": 50, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698251959, "dur": 3, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698251963, "dur": 30, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698251997, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698251999, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698252036, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698252038, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698252068, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698252069, "dur": 365, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698252438, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698252441, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698252481, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698252484, "dur": 1173, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698253663, "dur": 7, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698253672, "dur": 44, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698253720, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698253723, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698253760, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698253761, "dur": 144, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698253908, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698253910, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698253939, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698253942, "dur": 22, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698253968, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698253996, "dur": 325, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254325, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254328, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254380, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254443, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254445, "dur": 57, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254506, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254508, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254541, "dur": 15, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254559, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254582, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254584, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254639, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254664, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254666, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254693, "dur": 55, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254751, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254753, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254791, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254822, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254848, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254850, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254878, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254880, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254913, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254941, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254967, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698254991, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255057, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255079, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255081, "dur": 234, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255318, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255320, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255351, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255372, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255395, "dur": 17, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255416, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255436, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255474, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255519, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255546, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255619, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255622, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255650, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255652, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255692, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255717, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255719, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255752, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255754, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255778, "dur": 9, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255789, "dur": 6, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255797, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255805, "dur": 6, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255813, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255844, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255868, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698255891, "dur": 178, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698256074, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698256113, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698256116, "dur": 1594, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698257715, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698257717, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698257753, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698257755, "dur": 4145, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698261914, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698261918, "dur": 145, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698262069, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698262073, "dur": 481, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698262559, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698262566, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698262614, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698262619, "dur": 362, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698262986, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698263021, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698263024, "dur": 2595, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698265629, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698265634, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698265687, "dur": 4, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698265692, "dur": 33, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698265728, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698265730, "dur": 211, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698265946, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698265948, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698265996, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698265998, "dur": 1122, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698267128, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698267132, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698267216, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698267218, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698267280, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698267287, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698267316, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698267318, "dur": 161, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698267483, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698267485, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698267531, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698267533, "dur": 652, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698268192, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698268195, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698268247, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698268249, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698268316, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698268318, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698268359, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698268363, "dur": 45, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698268412, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698268414, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698268451, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698268453, "dur": 718, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698269177, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698269180, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698269208, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698269210, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698269233, "dur": 200, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698269439, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698269441, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698269475, "dur": 215, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698269696, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698269737, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698269766, "dur": 317, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698270087, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698270097, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698270142, "dur": 35, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698270179, "dur": 25, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698270207, "dur": 11, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698270220, "dur": 47, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698270270, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698270272, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698270288, "dur": 589, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698270883, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698270886, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698270922, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698270924, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698270969, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698270971, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698271000, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698271003, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698271073, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698271075, "dur": 581, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698271662, "dur": 26, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698271690, "dur": 3491, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698275191, "dur": 34, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698275227, "dur": 77, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698275307, "dur": 40, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698275349, "dur": 585, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698275943, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698275947, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698275984, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698275988, "dur": 299, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698276295, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698276298, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698276344, "dur": 41, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698276387, "dur": 70, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698276462, "dur": 53, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698276531, "dur": 192, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698276728, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698276732, "dur": 30, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698276764, "dur": 19, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698276785, "dur": 1223, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698278018, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698278022, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698278065, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698278069, "dur": 538, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698278614, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698278618, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698278640, "dur": 130, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698278774, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698278835, "dur": 258, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698279097, "dur": 21, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698279120, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698279122, "dur": 659, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698279788, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698279792, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698279821, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698279850, "dur": 134, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698279994, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698279997, "dur": 198, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698280198, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698280202, "dur": 2705, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698282920, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698282925, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698282979, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698283007, "dur": 266, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698283279, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698283347, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698283352, "dur": 2968, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698286331, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698286336, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698286431, "dur": 54, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698286487, "dur": 57, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698286548, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698286553, "dur": 353, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698286911, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698286913, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698286953, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698286971, "dur": 88, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698287063, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698287065, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698287103, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698287107, "dur": 744, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698287857, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698287859, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698287887, "dur": 151, "ph": "X", "name": "ProcessMessages 120", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698288042, "dur": 312, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698288359, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698288362, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698288399, "dur": 14, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698288415, "dur": 24, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698288442, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698288486, "dur": 14, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698288501, "dur": 23, "ph": "X", "name": "ProcessMessages 10", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698288526, "dur": 30, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698288560, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698288565, "dur": 453, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698289023, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698289026, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698289086, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698289106, "dur": 47, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698289156, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698289159, "dur": 2247, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698291432, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698291438, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698291510, "dur": 63, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698291575, "dur": 17, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698291594, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698291596, "dur": 353, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698291954, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698291957, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698292002, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698292043, "dur": 34, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698292080, "dur": 22, "ph": "X", "name": "ProcessMessages 46", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698292104, "dur": 212, "ph": "X", "name": "ReadAsync 46", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698292322, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698292325, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698292374, "dur": 14, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698292391, "dur": 36, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698292430, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698292458, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698292460, "dur": 482, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698292949, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698292952, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698292999, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698293027, "dur": 984, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698294021, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698294026, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698294099, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698294105, "dur": 72, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698294182, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698294202, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698294271, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698294300, "dur": 25, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698294326, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698294329, "dur": 1848, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698296191, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698296196, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698296281, "dur": 32, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698296316, "dur": 2365, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698298691, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698298695, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698298729, "dur": 20, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698298750, "dur": 154, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698298915, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698298919, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698298977, "dur": 26, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698299004, "dur": 32, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698299039, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698299042, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698299072, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698299075, "dur": 107, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698299187, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698299235, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698299250, "dur": 28, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698299282, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698299321, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698299324, "dur": 382, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698299712, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698299714, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698299785, "dur": 1, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698299787, "dur": 34, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698299825, "dur": 54, "ph": "X", "name": "ProcessMessages 10", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698299881, "dur": 470, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698300357, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698300360, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698300412, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698300435, "dur": 1771, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698302215, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698302219, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698302280, "dur": 39, "ph": "X", "name": "ProcessMessages 126", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698302321, "dur": 882, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698303213, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698303216, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698303273, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698303300, "dur": 173456, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698476764, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698476769, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698476808, "dur": 27, "ph": "X", "name": "ProcessMessages 1510", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698476836, "dur": 1583, "ph": "X", "name": "ReadAsync 1510", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698478428, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698478431, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698478506, "dur": 23, "ph": "X", "name": "ProcessMessages 3060", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698478532, "dur": 8540, "ph": "X", "name": "ReadAsync 3060", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698487080, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698487084, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698487154, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 14000, "tid": 25769803776, "ts": 1750356698487156, "dur": 8901, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 14000, "tid": 20, "ts": 1750356698497801, "dur": 10547, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 14000, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 14000, "tid": 21474836480, "ts": 1750356698199826, "dur": 57204, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 14000, "tid": 21474836480, "ts": 1750356698257031, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 14000, "tid": 21474836480, "ts": 1750356698257032, "dur": 36, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 14000, "tid": 20, "ts": 1750356698508352, "dur": 22, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 14000, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 14000, "tid": 17179869184, "ts": 1750356698194723, "dur": 301558, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 14000, "tid": 17179869184, "ts": 1750356698194861, "dur": 4448, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 14000, "tid": 17179869184, "ts": 1750356698496291, "dur": 197, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 14000, "tid": 17179869184, "ts": 1750356698496310, "dur": 24, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 14000, "tid": 17179869184, "ts": 1750356698496491, "dur": 2, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 14000, "tid": 20, "ts": 1750356698508379, "dur": 11, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1750356698229039, "dur": 137, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750356698229239, "dur": 2974, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750356698232224, "dur": 699, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750356698233050, "dur": 66, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1750356698233117, "dur": 486, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750356698234731, "dur": 305, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_C412A4EF09BA29D5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750356698235063, "dur": 470, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_D65E8F5246B9B178.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750356698235574, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_C33E9E8B76E14557.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750356698235841, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_167AF4DBC06E2AE6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750356698236638, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_93BF4D5398025317.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750356698236839, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_368E6BDD3809CE5E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750356698236909, "dur": 137, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_AF1FC69261D1616D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750356698237194, "dur": 174, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1750356698237394, "dur": 448, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750356698237865, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750356698238217, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750356698239394, "dur": 238, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750356698239687, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_9A20CFD48BC11F43.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750356698239817, "dur": 488, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750356698240337, "dur": 368, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750356698240796, "dur": 158, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750356698241009, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1750356698241194, "dur": 827, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750356698242653, "dur": 208, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750356698243563, "dur": 308, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/TacticalCombatSystem.UI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750356698244022, "dur": 2790, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Splines.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1750356698246824, "dur": 511, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Cinemachine.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1750356698247417, "dur": 337, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1750356698247760, "dur": 157, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1750356698247925, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1750356698233642, "dur": 14887, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750356698248545, "dur": 235473, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750356698484019, "dur": 279, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750356698486876, "dur": 51, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750356698486965, "dur": 2466, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1750356698233832, "dur": 14800, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698248669, "dur": 845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_3A585D315D7CDFE1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698249518, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698249774, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698249909, "dur": 514, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_BF8B01E2682946F2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698250426, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_81157A417001B5EA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698250500, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698250558, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_37A91187EFAA1E4D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698250704, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698250872, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_37A91187EFAA1E4D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698250950, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_908A7CF0F95B5EED.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698251113, "dur": 655, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IdentifiersModule.dll_908A7CF0F95B5EED.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698251769, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_9A8E98434D7100DD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698252033, "dur": 364, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_E84E2AFCCEFD9CC7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698252442, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698252686, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698253187, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698253251, "dur": 362, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_4B6EE49D6AF37933.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698253617, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_5C4DA00CC016DBF5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698253972, "dur": 414, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_5C4DA00CC016DBF5.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698254393, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698254558, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698254616, "dur": 10772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750356698265390, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698265511, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698265635, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698265847, "dur": 978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750356698266827, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698267104, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698267374, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750356698267956, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698268125, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698268256, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698268323, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750356698268944, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698269128, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698269331, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750356698269834, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698269989, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698270167, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750356698270655, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698270878, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698271044, "dur": 575, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698271622, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750356698272217, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698272318, "dur": 643, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698272970, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1750356698273141, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698273200, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750356698273652, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698273740, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1750356698274132, "dur": 66, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1750356698274495, "dur": 202079, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750356698233883, "dur": 14780, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698248674, "dur": 1093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_EF62AA7CD312F942.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750356698249768, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698249866, "dur": 686, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_EF62AA7CD312F942.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750356698250554, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_167AF4DBC06E2AE6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750356698250697, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698251016, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698251109, "dur": 1265, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_C44092216EEC6649.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750356698252376, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_5592C7A2A0C2A571.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750356698252666, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_9A7786E72A6B5E8C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750356698252952, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_1F322CB3825CC582.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750356698253147, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698253257, "dur": 407, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_E0FCC25CAA3CC2C8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750356698253668, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_28E7CDA6A9657C3E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750356698253931, "dur": 381, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750356698254545, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698254753, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698254991, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1750356698255399, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1750356698255465, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698255772, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698256218, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698256918, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698257926, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698258414, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698258866, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698259326, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698259873, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698260396, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698260911, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698261401, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698262458, "dur": 1008, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@b10ae3e6f84c\\UnityEditor.TestRunner\\GUI\\Views\\TestListGUIBase.cs"}}, {"pid": 12345, "tid": 2, "ts": 1750356698261918, "dur": 2215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698264134, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698264655, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698265191, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698265532, "dur": 1487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698267023, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698267163, "dur": 5576, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 2, "ts": 1750356698272741, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Camera.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750356698272940, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1750356698273137, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Battle.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750356698273626, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698273746, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750356698274237, "dur": 96, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1750356698274498, "dur": 203876, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1750356698483920, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698233918, "dur": 14764, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698248711, "dur": 1123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_163A7D00BA7D065C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750356698249884, "dur": 553, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_163A7D00BA7D065C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750356698250439, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_8F9DC40C34B6425C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750356698250527, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698250701, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_9A1C02F8E98C469D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750356698250830, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698251093, "dur": 561, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BD985A39D270C9B7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750356698251656, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_F0A87365319D71DD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750356698251722, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698251806, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698251940, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_9DA4C3C21022D7A6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750356698252035, "dur": 433, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_9DA4C3C21022D7A6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750356698252481, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_1E51E4CC49144E2E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750356698252565, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_1E51E4CC49144E2E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750356698252660, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_D9B7D3234F369077.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750356698252860, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698253001, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_9F4733395C159688.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750356698253096, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A9C666157FD2E5AB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750356698253193, "dur": 323, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A9C666157FD2E5AB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750356698253627, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_2E76C9AC399D6AFC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750356698253912, "dur": 309, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_2A580A41823500EB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750356698254281, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698254733, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698254890, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1750356698255182, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698255430, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1750356698256021, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698256862, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698257762, "dur": 1186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698258949, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698259627, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698260103, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698260607, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698261104, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698261330, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698265252, "dur": 6700, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1750356698271954, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750356698272104, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698272181, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.Interfaces.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750356698272729, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698273115, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1750356698273344, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1750356698273902, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698274092, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698274234, "dur": 4316, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Battle.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750356698278560, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698278811, "dur": 8684, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Cinemachine.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750356698287539, "dur": 3587, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Splines.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750356698295160, "dur": 79, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698291186, "dur": 4069, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1750356698295487, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750356698295613, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1750356698295925, "dur": 4336, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1750356698300264, "dur": 183794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750356698234033, "dur": 14762, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750356698248813, "dur": 957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_CE52468F2AF94D3E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750356698249771, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750356698249980, "dur": 651, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_564C15F1D50DDD04.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750356698250632, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_5E1525907236878A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750356698250878, "dur": 796, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_D910E96BA6DA4E34.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750356698251676, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E0273E4BBA1C33C0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750356698251727, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750356698251811, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_42F7D41B93B94AC7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750356698251991, "dur": 367, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_9283034F04690CF9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750356698252370, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_C8FB050F2C14EF55.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750356698252597, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F591B0E4ADD29A56.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750356698252653, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750356698252797, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_747257F2B95C7621.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750356698253118, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_567619744B52B229.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750356698253174, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_988D394CD4FBAB22.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750356698253239, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750356698253290, "dur": 392, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_988D394CD4FBAB22.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750356698253808, "dur": 486, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1750356698254332, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750356698254406, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1750356698254609, "dur": 7080, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750356698261691, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750356698261829, "dur": 719, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750356698271004, "dur": 565, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750356698265162, "dur": 7504, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750356698272668, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1750356698273164, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750356698273286, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750356698273393, "dur": 5162, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750356698278562, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750356698286598, "dur": 208, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750356698278809, "dur": 8076, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Cinemachine.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1750356698286894, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750356698286970, "dur": 4298, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Splines.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1750356698291277, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750356698291451, "dur": 7088, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1750356698298550, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1750356698298745, "dur": 3155, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1750356698301904, "dur": 182112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698234068, "dur": 14761, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698248846, "dur": 985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_16F59DAED9B12573.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750356698249833, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698249903, "dur": 377, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_16F59DAED9B12573.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750356698250422, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_B1A9D295A62A6B25.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750356698250529, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698250890, "dur": 675, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_B1A9D295A62A6B25.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750356698251567, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F5F9D4CEA3FE42FE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750356698251700, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_6648D96BDBBB29C0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750356698251947, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_A02BE6282932C1CB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750356698252060, "dur": 576, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_A02BE6282932C1CB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750356698252665, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_8E9AC1970AF2CEA5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750356698253177, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_7614754D03CD3CB4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750356698253289, "dur": 379, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_7614754D03CD3CB4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750356698253949, "dur": 432, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750356698254415, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698254652, "dur": 245, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1750356698254940, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1750356698255194, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698255449, "dur": 2152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698257611, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698258175, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698258639, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698259101, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698259948, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698260552, "dur": 497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698261049, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698261299, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698261928, "dur": 620, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@b10ae3e6f84c\\UnityEditor.TestRunner\\TestLaunchers\\PlayerLauncher.cs"}}, {"pid": 12345, "tid": 5, "ts": 1750356698261883, "dur": 1488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698263372, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698264039, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698264834, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698265363, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698265694, "dur": 6239, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750356698271935, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750356698272107, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698272177, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750356698272623, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698272754, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1750356698272959, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1750356698273459, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698273645, "dur": 3011, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750356698276662, "dur": 1189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698277874, "dur": 8262, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750356698286147, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698286380, "dur": 2531, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Core.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1750356698288917, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698288987, "dur": 5113, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1750356698294111, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1750356698294179, "dur": 4956, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1750356698299195, "dur": 3904, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 5, "ts": 1750356698303104, "dur": 180909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698234093, "dur": 14762, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698248872, "dur": 1097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9B5BF5B045BFFED8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750356698250018, "dur": 617, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_9B5BF5B045BFFED8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750356698250643, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_AC3B8AED076F4CEB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750356698251070, "dur": 468, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_C33E9E8B76E14557.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750356698251540, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_D65E8F5246B9B178.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750356698251720, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1FC85B3DDABA9491.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750356698251979, "dur": 341, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_A36FF8077F8E7B00.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750356698252494, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_97DB01ADA77F4791.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750356698252686, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_93BF4D5398025317.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750356698252856, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698252944, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_609C16FA6C001C3C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750356698253231, "dur": 375, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_E3D9DEC668D47C33.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750356698253610, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_AE7AE761007ACD6C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750356698253759, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750356698253956, "dur": 866, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1750356698254823, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1750356698255169, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1750356698255259, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698255452, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698255567, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698255665, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698255795, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698256597, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698257737, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698258525, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698259217, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698259934, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698260728, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698261465, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698262456, "dur": 858, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@b10ae3e6f84c\\UnityEditor.TestRunner\\GUI\\TestAssets\\IActiveFolderTemplateAssetCreator.cs"}}, {"pid": 12345, "tid": 6, "ts": 1750356698262348, "dur": 1537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698263886, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698265008, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698265206, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698265689, "dur": 6216, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1750356698271909, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750356698272157, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698272219, "dur": 1366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750356698273586, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698273715, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750356698273883, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1750356698274537, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698274734, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750356698274917, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1750356698275097, "dur": 694, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698275816, "dur": 3912, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Core.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750356698279751, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698279887, "dur": 8513, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1750356698288449, "dur": 4399, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1750356698292857, "dur": 990, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698299589, "dur": 93, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698293875, "dur": 5818, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1750356698299696, "dur": 184272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1750356698483969, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698234165, "dur": 14712, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698248899, "dur": 1002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2A38684436712F05.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750356698249902, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698249975, "dur": 606, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_2A38684436712F05.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750356698250582, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_606993901B0ABE27.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750356698250876, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_606993901B0ABE27.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750356698251129, "dur": 1215, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_F9164AECFF7EBE63.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750356698252345, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_2DC85C64375951B1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750356698252663, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698252900, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_416ADE92277E457E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750356698253015, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_BFE30F898F3B693C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750356698253157, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698253211, "dur": 359, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_1A8907DE4FA741CC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750356698253630, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_361A65A56C957CE2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750356698253857, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698253924, "dur": 427, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1750356698254433, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698254825, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698254959, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698255041, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1750356698255340, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698255528, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698255755, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698256581, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698257757, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698258471, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698259175, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698259884, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698260657, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698261428, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698262388, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698263185, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698263999, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698265154, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698265542, "dur": 1474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698267021, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698267179, "dur": 4751, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750356698271934, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1750356698272095, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698272164, "dur": 758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1750356698272923, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698273065, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698273203, "dur": 3027, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Interfaces.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750356698276257, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698276357, "dur": 6427, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Splines.dll"}}, {"pid": 12345, "tid": 7, "ts": 1750356698282797, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698283142, "dur": 5148, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Battle.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750356698288353, "dur": 70, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698288425, "dur": 3437, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750356698291869, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1750356698292214, "dur": 6641, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/TacticalCombatSystem.Interfaces.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750356698298919, "dur": 3191, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1750356698302113, "dur": 181898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698234230, "dur": 14662, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698248893, "dur": 1006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_D437D5B52E940013.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750356698249929, "dur": 606, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_D437D5B52E940013.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750356698250537, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_1BC2F9751197887F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750356698250935, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698251102, "dur": 593, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D0F1A61EB4189FC9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750356698251697, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_51174E3C056538C2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750356698251798, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_1A4CC6E2C0ED3512.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750356698252008, "dur": 381, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_1A4CC6E2C0ED3512.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750356698252390, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_47DDA486F86DF89C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750356698252526, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_BEDC949A65E52DA7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750356698252710, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_F2C1277DFF099C79.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750356698253228, "dur": 362, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DDA69634F904366D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750356698253595, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_B701B6B92B8026DD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750356698253900, "dur": 401, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_997BF679851B602C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750356698254633, "dur": 229, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750356698254872, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698255015, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1750356698255411, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1750356698255733, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698256368, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698259291, "dur": 772, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.2.0b2/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 8, "ts": 1750356698257280, "dur": 2784, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698260064, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698260824, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698261896, "dur": 610, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@b10ae3e6f84c\\UnityEditor.TestRunner\\TestRun\\Data\\TestProgress.cs"}}, {"pid": 12345, "tid": 8, "ts": 1750356698261557, "dur": 1510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698263068, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698263864, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698265199, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698265513, "dur": 1573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698267087, "dur": 1009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698268100, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698268211, "dur": 3901, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1750356698272115, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750356698272303, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750356698272471, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1750356698273020, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698273125, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/TacticalCombatSystem.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1750356698273363, "dur": 5213, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Cinemachine.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750356698278582, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698278878, "dur": 8813, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750356698287747, "dur": 4193, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1750356698291948, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698292345, "dur": 6464, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Cinemachine.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1750356698298815, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1750356698298965, "dur": 3128, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Unity.Splines.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1750356698302095, "dur": 181920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1750356698492722, "dur": 1775, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 14000, "tid": 20, "ts": 1750356698508444, "dur": 33, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 14000, "tid": 20, "ts": 1750356698508605, "dur": 3262, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 14000, "tid": 20, "ts": 1750356698497737, "dur": 14180, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}